@Library('cvc-jen<PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "corp-wl-vhs-bff",
    "git": {
        "repositoryUrl": "******************:Desenvolvimento-MS/corp-wl-vhs/corp-wl-vhs-bff.git"
    },
    "technology": {
        "name": "nodejs",
        "version": "18.20.4",
        "buildCommands": {
            "installDep": "npm install",
            "buildApp": "npm run build"
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "corp-wl-vhs"
    }
}
""")

final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {
                        
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
}
