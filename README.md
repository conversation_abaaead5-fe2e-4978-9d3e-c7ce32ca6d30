## Rodar a aplicação

- Versão do Node: 18.20.4

```sh
npm install
npm run qa
```

## Url Local

- [http://localhost:8080/](http://localhost:8080/)

- Para rodar local passar o header `gtw-agent-sign: <PERSON><PERSON><PERSON>` ou `gtw-agent-sign: WEB`

### Listar Propriedades

```sh
curl -X POST http://localhost:8080/api/property \
  -H "Content-Type: application/json" \
  -H "api-token: SEU_TOKEN_JWT" \
  -H "gtw-agent-sign: LOJ" \
  -H "gtw-transaction-id: test-$(date +%s)" \
  -d '{
    "checkin": "02/09/2024",
    "checkout": "18/09/2024",
    "pax": "30,30",
    "packageGroup": "STANDALONE",
    "zone": "1",
    "zoneName": "Orlando, FL",
    "specialOffersMock": false
  }'
```

### Detalhes da Propriedade

```sh
curl -X POST http://localhost:8080/api/property/details \
  -H "Content-Type: application/json" \
  -H "api-token: SEU_TOKEN_JWT" \
  -H "gtw-agent-sign: LOJ" \
  -H "gtw-transaction-id: test-$(date +%s)" \
  -d '{
    "id": "ID_DA_PROPRIEDADE",
    "checkin": "02/09/2024",
    "checkout": "18/09/2024",
    "pax": "30,30",
    "packageGroup": "STANDALONE",
    "zoneName": "Orlando, FL"
  }'
```

### Verificar Disponibilidade (hasAvail)

```sh
curl -X GET "http://localhost:8080/api/property/hasAvail/SEU_KEY_RATE_TOKEN" \
  -H "api-token: SEU_TOKEN_JWT" \
  -H "gtw-agent-sign: LOJ" \
  -H "gtw-transaction-id: test-$(date +%s)"
```

### Listar Zonas

```sh
curl -X GET http://localhost:8080/api/zone \
  -H "api-token: SEU_TOKEN_JWT" \
  -H "gtw-agent-sign: LOJ" \
  -H "gtw-transaction-id: test-$(date +%s)"
```

### Listar Amenidades

```sh
curl -X GET http://localhost:8080/api/amenity \
  -H "api-token: SEU_TOKEN_JWT" \
  -H "gtw-agent-sign: LOJ" \
  -H "gtw-transaction-id: test-$(date +%s)"
```

**Nota:** Para gerar um token JWT válido, você pode usar:

```javascript
const jwt = require("jsonwebtoken");
const payload = {
  credential: {
    agentSign: "WEB",
    branchId: "1000",
  },
};
const token = jwt.sign(payload, "1234567890123456", { expiresIn: "3h" });
```

[![Build status](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-corp-wl-vhs-bff/badge/icon)] (<http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-corp-wl-vhs-bff/>)
[![Quality Gate](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=alert_status)](https://sonar.app.cvc.com.br/dashboard?id=corp-wl-vhs-bff)
[![Coverage](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=coverage)](https://sonar.app.cvc.com.br/component_measures?id=corp-wl-vhs-bff&metric=Coverage)
[![Maintainnability](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=sqale_rating)] (<https://sonar.app.cvc.com.br/component_measures?id=corp-wl-vhs-bff&metric=Maintainability>)
[![Security](https://sonar.app.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=security_rating)] (<https://sonar.app.cvc.com.br/component_measures?id=corp-wl-vhs-bff&metric=Security>)

# Corp WL VHS BFF

Backend for Frontend (BFF) para propriedades e hospedagem da CVC.

## Arquitetura

Esta aplicação implementa o padrão Clean Architecture com as seguintes camadas:

- **Interface Adapters**: Controllers que expõem endpoints REST
- **Application Business Rules**: Brokers que fazem integração com serviços externos
- **Enterprise Business Rules**: Utilitários e regras de negócio
- **Frameworks & Drivers**: Banco de dados, Redis, middlewares

## Integrações

### VHC (Vacation Home Connection)

- Fornece dados das propriedades
- Gerencia disponibilidade
- Processa reservas

### Drools

- Aplica regras de precificação
- Calcula impostos e taxas
- Define markups

### Redis

- Cache de rateTokens
- Controle de visitas às propriedades
- Armazenamento temporário de dados

### Oracle DB

- Configurações de cidades origem/destino
- Dados de branches e agentes

## Documentação da API

**⚠️ Este projeto não possui Swagger configurado.**

Para testar localmente, é necessário incluir o header `Gtw-Agent-Sign: LOJ` ou `Gtw-Agent-Sign: WEB` em todas as requisições.

## Endpoints

- **Local:** <http://localhost:8080>
- **TI:** (configurar conforme necessário)
- **QA:** (configurar conforme necessário)
- **PROD:** (configurar conforme necessário)

## Testes

Execute a linha de comando:

```sh
npm run test:cov
```

## Query para logs no Kibana

<https://kibana.services.cvc.com.br/app/kibana#/discover?_g=()&_a=(columns:!(message),index:'5725b180-ba9d-11e8-be0f-396272e87c50',interval:auto,query:(language:kuery,query:'kubernetes.namespace_name:%20%22corp-wl-vhs-bff%22%20'),sort:!('@timestamp',desc))>
